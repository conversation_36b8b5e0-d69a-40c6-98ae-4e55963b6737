import { useEffect, useState } from "react";
import { useQuery, useMutation, useInfiniteQuery } from "@tanstack/react-query";

import { useAppContext } from "@/context/app";
import type {
  GroupResponse,
  GroupsResponse,
  GroupCohortsResponse,
  CohortResponse,
  CourseResponse,
  LessonResponse,
  MyGroupsResponse,
  LiveClassesResponse,
  ProfileResponse,
  APIResponse,
  UserProfile,
  APISuccessResponse,
  ProfileByIdResponse,
  LiveClassResponse,
  NotificationsResponse,
  ModuleResponse,
} from "@/lib/api/types";
import { queryClient } from "@/lib/providers/query-client";

const API_URL = process.env.EXPO_PUBLIC_API_DOMAIN;

function useProfile() {
  return useQuery<APISuccessResponse<UserProfile>>({
    queryKey: ["profile"],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/profile`);
      const data = (await response.json()) as APIResponse<UserProfile>;
      console.log("profile", data);
      if (!data.success) {
        throw new Error(data.data.message);
      }
      return data;
    },
  });
}

function useEditProfile() {
  return useMutation({
    mutationFn: async ({
      firstName,
      lastName,
      age,
      avatarURL,
      bio,
      gender,
      interestedTopics,
      phoneNumber,
    }: {
      firstName: string;
      lastName: string;
      age?: number;
      avatarURL?: string;
      bio?: string;
      gender: string | null;
      interestedTopics?: string[];
      phoneNumber?: string;
    }) => {
      const response = await fetch(`${API_URL}/profile`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          firstName,
          lastName,
          age,
          avatarURL,
          bio,
          gender,
          interestedTopics,
          phoneNumber,
        }),
      });
      const data = await response.json();
      console.log("edit profile", data);
      if (!data.success) {
        throw new Error(data.data.message);
      }
      return data;
    },
    onMutate: async (newProfile) => {
      // Cancel any outgoing refetches
      // (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: ["profile"] });

      // Snapshot the previous value
      const previousProfile = queryClient.getQueryData<ProfileResponse>([
        "profile",
      ]);

      // Optimistically update to the new value
      queryClient.setQueryData(["profile"], (old: ProfileResponse) => ({
        ...old,
        data: {
          ...old.data,
          ...newProfile,
        },
      }));

      // Return a context object with the snapshotted value
      return { previousProfile };
    },
    onError: (err, newProfile, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousProfile) {
        queryClient.setQueryData(["profile"], context.previousProfile);
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({
        queryKey: ["profile"],
      });
    },
  });
}

function useGroups(limit: number = 20) {
  return useInfiniteQuery<GroupsResponse>({
    queryKey: ["groups"],
    queryFn: async ({ pageParam = 0 }) => {
      const response = await fetch(
        `${API_URL}/groups?limit=${limit}&offset=${pageParam}`
      );
      const data = await response.json();
      return data;
    },
    getNextPageParam: (lastPage, allPages) => {
      if (!lastPage.success) return undefined;

      const totalFetched = allPages.length * limit;
      const hasMore = totalFetched < lastPage.total;

      return hasMore ? totalFetched : undefined;
    },
    initialPageParam: 0,
  });
}

function useMyGroups() {
  return useQuery<{
    success: boolean;
    groups: {
      byId: { [id: string]: MyGroupsResponse["data"]["groups"][0] };
      order: string[];
    };
  }>({
    queryKey: ["my", "groups"],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_URL}/me/groups`);
        const data = (await response.json()) as MyGroupsResponse;

        if (data.success) {
          // Normalization
          let myGroups: {
            byId: {
              [externalId: string]: MyGroupsResponse["data"]["groups"][0];
            };
            order: string[];
          } = {
            byId: {},
            order: [],
          };
          data.data.groups.forEach((group) => {
            myGroups.byId[group.externalId] = group;
            myGroups.order.push(group.externalId);
          });

          return { success: true, groups: myGroups };
        }

        return { success: false, groups: { byId: {}, order: [] } };
      } catch (e) {
        throw e;
      }
    },
  });
}

function useGroup(id: string | number | null) {
  return useQuery<GroupResponse>({
    queryKey: ["groups", id],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/groups/${id}`);
      const data = await response.json();
      return data;
    },
    enabled: !!id,
  });
}

function useGroupCohorts(groupId: string | number) {
  return useQuery<GroupCohortsResponse>({
    queryKey: ["groups", groupId, "cohorts"],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/groups/${groupId}/cohorts`);
      const data = await response.json();
      return data;
    },
  });
}

function useCohort(
  groupId: string | number | null,
  cohortId: string | number | null
) {
  return useQuery<CohortResponse>({
    queryKey: ["groups", groupId, "cohorts", cohortId],
    queryFn: async () => {
      const response = await fetch(
        `${API_URL}/groups/${groupId}/cohorts/${cohortId}`
      );
      const data = await response.json();
      return data;
    },
    enabled: !!groupId && !!cohortId, // Only run the query when both groupId and cohortId are set
  });
}

function useCourse(id: string | number) {
  return useQuery<CourseResponse>({
    queryKey: ["courses", id],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/courses/${id}`);
      const data = await response.json();
      return data;
    },
  });
}

function useLesson(
  courseId: string | number,
  sectionId: string | number,
  lessonId: string | number
) {
  return useQuery<LessonResponse>({
    queryKey: ["courses", courseId, "sections", sectionId, "lessons", lessonId],
    queryFn: async () => {
      const response = await fetch(
        `${API_URL}/courses/${courseId}/sections/${sectionId}/lessons/${lessonId}`
      );
      const data = await response.json();
      return data;
    },
  });
}

function useLiveClasses(
  groupId: string | number,
  cohortId: string | number,
  moduleId: string | number,
  isPresent: boolean,
  limit: number = 20
) {
  return useInfiniteQuery<LiveClassesResponse>({
    queryKey: [
      "groups",
      groupId,
      "cohorts",
      cohortId,
      "modules",
      moduleId,
      "live-classes",
      "isPresent",
      isPresent,
    ],
    queryFn: async ({ pageParam = 0 }) => {
      const response = await fetch(
        `${API_URL}/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}/live-classes?isPresent=${isPresent}&limit=${limit}&offset=${pageParam}`
      );
      const data = await response.json();
      return data;
    },
    getNextPageParam: (lastPage, allPages) => {
      if (!lastPage.success) return undefined;

      const totalFetched = allPages.length * limit;
      const hasMore = totalFetched < lastPage.total;

      return hasMore ? totalFetched : undefined;
    },
    initialPageParam: 0,
  });
}

function useLiveClass(
  groupId: string | number,
  cohortId: string | number,
  moduleId: string | number,
  liveClassId: string | number
) {
  return useQuery<LiveClassResponse>({
    queryKey: [
      "groups",
      groupId,
      "cohorts",
      cohortId,
      "modules",
      moduleId,
      "live-classes",
      liveClassId,
    ],
    queryFn: async () => {
      const response = await fetch(
        `${API_URL}/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}/live-classes/${liveClassId}`
      );
      const data = await response.json();
      console.log("live class", data);
      return data;
    },
  });
}

function useMyCohort(externalCohortId: string) {
  return useQuery<CohortResponse>({
    queryKey: ["me", "cohorts", externalCohortId],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/me/cohorts/${externalCohortId}`);
      const data = await response.json();
      console.log("my cohort", data);
      return data;
    },
  });
}

function useMyCohortModule(
  externalGroupId: string,
  externalCohortId: string,
  moduleId: string
) {
  return useQuery<ModuleResponse>({
    queryKey: ["me", "cohorts", externalCohortId, "modules", moduleId],
    queryFn: async () => {
      const response = await fetch(
        `${API_URL}/groups/${externalGroupId}/cohorts/${externalCohortId}/modules/${moduleId}`
      );
      const data = await response.json();
      console.log("my cohort module", data);
      return data;
    },
  });
}

function useJoinCohort() {
  return useMutation({
    mutationFn: async ({
      groupId,
      cohortId,
    }: {
      groupId: string;
      cohortId: string;
    }) => {
      const response = await fetch(
        `${API_URL}/groups/${groupId}/cohorts/${cohortId}/join`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      const data = await response.json();
      return data;
    },
  });
}

function useGenerateGetStreamToken() {
  return useMutation({
    mutationFn: async () => {
      const response = await fetch(`${API_URL}/getstream/token/generate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });
      const data = await response.json();

      return data;
    },
  });
}

function useCompleteLesson() {
  return useMutation({
    mutationFn: async ({
      courseId,
      sectionId,
      lessonId,
    }: {
      courseId: string;
      sectionId: string;
      lessonId: string;
    }) => {
      const response = await fetch(
        `${API_URL}/courses/${courseId}/sections/${sectionId}/lessons/${lessonId}/complete`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      const data = await response.json();
      return { courseId, sectionId, lessonId };
    },
    // onMutate: async (data) => {
    //   // Cancel any outgoing refetches
    //   // (so they don't overwrite our optimistic update)
    //   await queryClient.cancelQueries({ queryKey: ["courses", data?.courseId] });

    //   // Snapshot the previous value
    //   const previousCourse = queryClient.getQueryData([
    //     "courses",
    //     data?.courseId,
    //   ]);

    //   // Optimistically update to the new value
    //   queryClient.setQueryData(["courses", data?.courseId], (old: CourseResponse) => {
    //     if (!old?.data) return old;

    //     return {
    //       ...old,
    //       data: {
    //         ...old.data,
    //         sections: old.data.sections.map(section => {
    //           if (section.id === data?.sectionId) {
    //             return {
    //               ...section,
    //               lessons: section.lessons.map(lesson => {
    //                 if (lesson.id === data?.lessonId) {
    //                   return {
    //                     ...lesson,
    //                     isCompleted: true,
    //                   };
    //                 }
    //                 return lesson;
    //               })
    //             };
    //           }
    //           return section;
    //         })
    //       }
    //     };
    //   });

    //   // Return a context object with the snapshotted value
    //   return { previousCourse };
    // },
    onSuccess: (data) => {
      if (!data?.courseId || !data?.sectionId || !data?.lessonId) return;

      // Invalidate both the lesson and course queries
      queryClient.invalidateQueries({
        queryKey: [
          "courses",
          data?.courseId,
          "sections",
          data?.sectionId,
          "lessons",
          data?.lessonId,
        ],
      });

      queryClient.invalidateQueries({
        queryKey: ["courses", data?.courseId],
      });
    },
  });
}

function useRegisterPushNotificationToken() {
  return useMutation({
    mutationFn: async ({
      platform,
      token,
    }: {
      platform: string;
      token: string;
    }) => {
      const response = await fetch(`${API_URL}/notifications/device-tokens`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ token, platform }),
      });
      const data = await response.json();
      console.log("register push notification token", data);
      return data;
    },
  });
}

function useProfileById(supertokensUserId: string) {
  return useQuery<APISuccessResponse<ProfileByIdResponse>>({
    queryKey: ["profile", supertokensUserId],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/profile/${supertokensUserId}`);
      const data = (await response.json()) as APIResponse<ProfileByIdResponse>;
      if (!data.success) {
        throw new Error(data.data.message);
      }
      return data;
    },
    enabled: !!supertokensUserId,
  });
}

function useRegisterLiveClass() {
  return useMutation({
    mutationFn: async ({
      groupId,
      cohortId,
      moduleId,
      liveClassId,
    }: {
      groupId: string;
      cohortId: string;
      moduleId: string;
      liveClassId: string;
    }) => {
      const response = await fetch(
        `${API_URL}/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}/live-classes/${liveClassId}/register`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      const data = (await response.json()) as APIResponse<null>;
      console.log("register live class", data);
      return { ...data, groupId, cohortId, moduleId, liveClassId };
    },
    onSuccess: ({ success, groupId, cohortId, moduleId, liveClassId }) => {
      if (!success) return;

      // Invalidate both the lesson and course queries
      queryClient.invalidateQueries({
        queryKey: [
          "groups",
          groupId,
          "cohorts",
          cohortId,
          "modules",
          moduleId,
          "live-classes",
          liveClassId,
        ],
      });

      // invalidate the isPresent query because the user only able to
      // register to a live class that is not over yet
      queryClient.invalidateQueries({
        queryKey: [
          "groups",
          groupId,
          "cohorts",
          cohortId,
          "modules",
          moduleId,
          "live-classes",
          "isPresent",
          true,
        ],
      });
    },
  });
}

function useNotifications(limit: number = 20) {
  return useInfiniteQuery<NotificationsResponse>({
    queryKey: ["notifications"],
    queryFn: async ({ pageParam = 0 }) => {
      const response = await fetch(
        `${API_URL}/me/notifications?limit=${limit}&offset=${pageParam}`
      );
      const data = await response.json();
      console.log("Notifications", data);
      return data;
    },
    getNextPageParam: (lastPage, allPages) => {
      if (!lastPage.success) return undefined;

      const totalFetched = allPages.length * limit;
      const hasMore = totalFetched < lastPage.data.total;

      return hasMore ? totalFetched : undefined;
    },
    initialPageParam: 0,
  });
}

function useReadNotification() {
  return useMutation({
    mutationFn: async ({ notificationId }: { notificationId: string }) => {
      const response = await fetch(
        `${API_URL}/notifications/${notificationId}/read`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      const data = (await response.json()) as APIResponse<null>;
      console.log("register live class", data);
      return { ...data, notificationId };
    },
    onMutate: ({ notificationId }) => {},
    onSuccess: ({ success, notificationId }) => {
      if (!success) return;

      queryClient.invalidateQueries({
        queryKey: ["notifications"],
      });
    },
  });
}

function useGetUploadUrl() {
  return useMutation({
    mutationFn: async ({
      contentType,
      fileName,
    }: {
      contentType: string;
      fileName: string;
    }) => {
      const response = await fetch(`${API_URL}/internal/storage/upload`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contentType,
          fileName,
        }),
      });

      const result = await response.json();
      console.log("get upload url", result);

      if (!response.ok) {
        throw new Error(result.message || "Failed to get upload URL");
      }

      // Return the nested data object
      return result.data as { cdnUrl: string; uploadUrl: string };
    },
  });
}

function useUploadAvatar() {
  return useMutation({
    mutationFn: async ({
      uploadUrl,
      uri,
      fileType,
    }: {
      uploadUrl: string;
      uri: string;
      fileType: string;
    }) => {
      try {
        console.log("Starting upload to:", uploadUrl);
        console.log("File URI:", uri);
        console.log("Content Type:", fileType);

        // Try two different approaches for uploading

        // Approach 1: FormData (if the API expects multipart/form-data)
        try {
          const formData = new FormData();
          formData.append("file", {
            uri,
            type: fileType,
            name: `avatar.${fileType.split("/")[1]}`,
          } as any);

          const uploadResponse = await fetch(uploadUrl, {
            method: "PUT",
            body: formData,
          });

          if (uploadResponse.ok) {
            console.log("FormData upload successful");
            return uploadResponse;
          } else {
            console.log("FormData upload failed, trying raw binary...");
          }
        } catch (formDataError) {
          console.log("FormData approach failed:", formDataError);
        }

        // Approach 2: Raw binary data (common for S3 presigned URLs)
        const response = await fetch(uri);
        if (!response.ok) {
          throw new Error("Failed to read image file");
        }

        const blob = await response.blob();
        console.log("Blob size:", blob.size);

        const uploadResponse = await fetch(uploadUrl, {
          method: "PUT",
          headers: {
            "Content-Type": fileType,
          },
          body: blob,
        });

        console.log(
          "Raw binary upload response status:",
          uploadResponse.status
        );

        if (!uploadResponse.ok) {
          const errorText = await uploadResponse
            .text()
            .catch(() => "No error text available");
          console.log("Upload error response:", errorText);
          throw new Error(
            `Failed to upload avatar: ${uploadResponse.status} ${uploadResponse.statusText}`
          );
        }

        return uploadResponse;
      } catch (error) {
        console.error("Upload error details:", error);
        throw error;
      }
    },
  });
}

// Optimized GetStream feed hooks
function useFeedActivities(feedGroup: string, feedId: string) {
  const { streamClient } = useAppContext();

  return useInfiniteQuery({
    queryKey: ["feed", feedGroup, feedId],
    queryFn: async ({ pageParam = undefined }) => {
      if (!streamClient) {
        throw new Error("Stream client not initialized");
      }

      const feed = streamClient.feed(feedGroup, feedId);

      // Optimized query parameters based on Oracle recommendations
      const response = await feed.get({
        limit: 10, // Reduced from 25 for faster initial load
        id_lt: pageParam,
        enrich: true,
        reactions: {
          own: true,
          counts: true,
          recent: {
            kinds: ["comment"],
            limit: 2, // Only show 2 recent comments to avoid extra API calls
          },
        },
      });

      return response;
    },
    initialPageParam: undefined,
    getNextPageParam: (lastPage: any) => {
      if (lastPage.results.length < 10) return undefined;
      return lastPage.results[lastPage.results.length - 1]?.id;
    },
    enabled: !!streamClient,
    staleTime: 1000 * 60 * 5, // 5 minutes cache
    refetchOnWindowFocus: false,
  });
}

// Real-time feed subscription hook
function useFeedRealtime(feedGroup: string, feedId: string) {
  const { streamClient } = useAppContext();
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    if (!streamClient) return;

    let subscription: any = null;

    const setupRealtime = async () => {
      try {
        const feed = streamClient.feed(feedGroup, feedId);

        // Subscribe to real-time updates
        subscription = feed.subscribe((data: any) => {
          console.log("🔄 Real-time feed update:", data);
          setLastUpdate(new Date());

          // Handle different types of real-time events
          if (data.new && data.new.length > 0) {
            console.log("✨ New activities:", data.new);
            // Invalidate queries to refetch with new data
            queryClient.invalidateQueries({
              queryKey: ["feed", feedGroup, feedId],
            });
          }

          if (data.deleted && data.deleted.length > 0) {
            console.log("🗑️ Deleted activities:", data.deleted);
            // Invalidate queries to remove deleted activities
            queryClient.invalidateQueries({
              queryKey: ["feed", feedGroup, feedId],
            });
          }

          // Handle reaction updates (likes, comments)
          if (data.updated && data.updated.length > 0) {
            console.log("🔄 Updated activities:", data.updated);
            // Invalidate queries to refresh reaction counts
            queryClient.invalidateQueries({
              queryKey: ["feed", feedGroup, feedId],
            });
          }
        });

        setIsConnected(true);
        console.log(`🔗 Connected to real-time feed: ${feedGroup}:${feedId}`);
      } catch (error) {
        console.error("❌ Failed to setup real-time feed subscription:", error);
        setIsConnected(false);
      }
    };

    setupRealtime();

    // Cleanup subscription
    return () => {
      if (subscription && typeof subscription.cancel === "function") {
        subscription.cancel();
        console.log(
          `🔌 Disconnected from real-time feed: ${feedGroup}:${feedId}`
        );
      }
      setIsConnected(false);
    };
  }, [streamClient, feedGroup, feedId]);

  return { isConnected, lastUpdate };
}

function useFeedLike(feedGroup: string, feedId: string) {
  const { streamClient } = useAppContext();

  return useMutation({
    mutationFn: async ({
      activityId,
      isLiked,
      reactionId,
    }: {
      activityId: string;
      isLiked: boolean;
      reactionId?: string;
    }) => {
      if (!streamClient) {
        throw new Error("Stream client not initialized");
      }

      if (isLiked && reactionId) {
        return streamClient.reactions.delete(reactionId);
      } else {
        return streamClient.reactions.add("like", activityId);
      }
    },
    onMutate: async ({ activityId, isLiked }) => {
      // Optimistically update the cache
      const queryKey = ["feed", feedGroup, feedId];
      await queryClient.cancelQueries({ queryKey });

      const previousData = queryClient.getQueryData(queryKey);

      queryClient.setQueryData(queryKey, (old: any) => {
        if (!old) return old;

        return {
          ...old,
          pages: old.pages.map((page: any) => ({
            ...page,
            results: page.results.map((activity: any) => {
              if (activity.id !== activityId) return activity;

              const currentLikeCount = activity.reaction_counts?.like || 0;
              const hasOwnLike = activity.own_reactions?.like?.length > 0;

              return {
                ...activity,
                reaction_counts: {
                  ...activity.reaction_counts,
                  like: isLiked ? currentLikeCount - 1 : currentLikeCount + 1,
                },
                own_reactions: {
                  ...activity.own_reactions,
                  like: isLiked ? [] : [{ id: "temp-id" }],
                },
              };
            }),
          })),
        };
      });

      return { previousData };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousData) {
        queryClient.setQueryData(
          ["feed", feedGroup, feedId],
          context.previousData
        );
      }
    },
    onSettled: () => {
      // Refetch to sync with server
      queryClient.invalidateQueries({ queryKey: ["feed", feedGroup, feedId] });
    },
  });
}

function useFeedComment(feedGroup: string, feedId: string) {
  const { streamClient, userId } = useAppContext();
  const { data: profileData } = useProfile();

  return useMutation({
    mutationFn: async ({
      activityId,
      text,
    }: {
      activityId: string;
      text: string;
    }) => {
      if (!streamClient) {
        throw new Error("Stream client not initialized");
      }

      return streamClient.reactions.add("comment", activityId, { text });
    },
    onMutate: async ({ activityId, text }) => {
      // Optimistically update the cache
      const queryKey = ["feed", feedGroup, feedId];
      await queryClient.cancelQueries({ queryKey });

      const previousData = queryClient.getQueryData(queryKey);

      // Create optimistic comment with user data
      const optimisticComment = {
        id: `temp-${Date.now()}`,
        kind: "comment",
        activity_id: activityId,
        user_id: userId,
        user: {
          id: userId,
          data: {
            name:
              `${profileData?.data?.firstName || ""} ${
                profileData?.data?.lastName || ""
              }`.trim() || "Anonymous",
            image: profileData?.data?.avatarUrl,
            firstName: profileData?.data?.firstName,
            lastName: profileData?.data?.lastName,
          },
        },
        data: { text },
        created_at: new Date().toISOString(),
      };

      queryClient.setQueryData(queryKey, (old: any) => {
        if (!old) return old;

        return {
          ...old,
          pages: old.pages.map((page: any) => ({
            ...page,
            results: page.results.map((activity: any) => {
              if (activity.id !== activityId) return activity;

              const currentCommentCount =
                activity.reaction_counts?.comment || 0;

              // Add the optimistic comment to latest_reactions
              const updatedLatestReactions = {
                ...activity.latest_reactions,
                comment: [
                  optimisticComment,
                  ...(activity.latest_reactions?.comment || []).slice(0, 2),
                ],
              };

              return {
                ...activity,
                reaction_counts: {
                  ...activity.reaction_counts,
                  comment: currentCommentCount + 1,
                },
                latest_reactions: updatedLatestReactions,
              };
            }),
          })),
        };
      });

      return { previousData };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousData) {
        queryClient.setQueryData(
          ["feed", feedGroup, feedId],
          context.previousData
        );
      }
    },
    onSettled: (data, error, variables) => {
      // Refetch to sync with server
      queryClient.invalidateQueries({ queryKey: ["feed", feedGroup, feedId] });
      // Also invalidate the specific comment cache for this activity
      queryClient.invalidateQueries({
        queryKey: ["feed-comments", variables.activityId],
      });
    },
  });
}

function useFeedPost(feedGroup: string, feedId: string) {
  const { streamClient } = useAppContext();

  return useMutation({
    mutationFn: async ({
      text,
      attachment,
      ogData,
    }: {
      text: string;
      attachment?: {
        type: string;
        url: string;
        fileName?: string;
      };
      ogData?: any;
    }) => {
      if (!streamClient) {
        throw new Error("Stream client not initialized");
      }

      const feed = streamClient.feed(feedGroup, feedId);

      const activityData: any = {
        verb: "post",
        message: text,
        object: `cohort:${feedId}`,
        to: [`${feedGroup}:${feedId}`],
        time: new Date().toISOString(),
      };

      if (attachment) {
        activityData.attachments = [
          {
            type: attachment.type === "image" ? "image" : "file",
            [attachment.type === "image" ? "image_url" : "asset_url"]:
              attachment.url,
            custom: attachment.fileName
              ? { fileName: attachment.fileName }
              : {},
          },
        ];
      }

      if (ogData) {
        activityData.og = ogData;
      }

      return feed.addActivity(activityData);
    },
    onSuccess: () => {
      // Invalidate and refetch feed data
      queryClient.invalidateQueries({ queryKey: ["feed", feedGroup, feedId] });
    },
  });
}

function useFeedComments(activityId: string) {
  const { streamClient } = useAppContext();

  return useQuery({
    queryKey: ["feed-comments", activityId],
    queryFn: async () => {
      if (!streamClient) {
        throw new Error("Stream client not initialized");
      }

      const response = await streamClient.reactions.filter({
        activity_id: activityId,
        kind: "comment",
        limit: 50,
      });

      return response.results;
    },
    enabled: false, // Only fetch when explicitly triggered
    staleTime: 1000 * 60 * 2, // 2 minutes cache
  });
}

function useUpdateFeedActivity(feedGroup: string, feedId: string) {
  return useMutation({
    mutationFn: async ({
      activities,
    }: {
      activities: Array<{
        id?: string;
        actor: string;
        verb: string;
        object: string;
        target?: string;
        foreignId?: string;
        time: string;
        message?: string;
        to?: string[];
        attachments?: any[];
        og?: any;
        extra?: any;
      }>;
    }) => {
      // Transform activities to match the API specification
      const transformedActivities = activities.map((activity) => ({
        actor: activity.actor,
        verb: activity.verb,
        object: activity.object,
        target: activity.target || "",
        foreignId: activity.foreignId || activity.id || "",
        time: activity.time,
        extra: {
          ...(activity.message && { message: activity.message }),
          ...(activity.attachments && { attachments: activity.attachments }),
          ...(activity.og && { og: activity.og }),
          ...(activity.to && { to: activity.to }),
          ...activity.extra,
        },
      }));

      const response = await fetch(`${API_URL}/getstream/activities`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ activities: transformedActivities }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `Failed to update activity: ${response.status}`
        );
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch feed data
      queryClient.invalidateQueries({ queryKey: ["feed", feedGroup, feedId] });
    },
  });
}

export {
  useProfile,
  useEditProfile,
  useGroups,
  useMyGroups,
  useGroup,
  useGroupCohorts,
  useCourse,
  useCohort,
  useLesson,
  useLiveClasses,
  useLiveClass,
  useMyCohort,
  useMyCohortModule,
  useJoinCohort,
  useGenerateGetStreamToken,
  useCompleteLesson,
  useRegisterPushNotificationToken,
  useProfileById,
  useRegisterLiveClass,
  useNotifications,
  useReadNotification,
  useGetUploadUrl,
  useUploadAvatar,
  useFeedActivities,
  useFeedRealtime,
  useFeedLike,
  useFeedComment,
  useFeedPost,
  useFeedComments,
  useUpdateFeedActivity,
};
