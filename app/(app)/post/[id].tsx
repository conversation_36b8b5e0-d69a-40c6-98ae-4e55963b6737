import { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  SafeAreaView,
} from "react-native";
import { Stack, useLocalSearchParams, router } from "expo-router";
import {
  connect,
  type EnrichedActivity,
  type EnrichedReaction,
} from "getstream";
import { ArrowLeft, Send } from "lucide-react-native";
import { Image } from "expo-image";
import { useQueryClient } from "@tanstack/react-query";

import { useAppContext } from "@/context/app";
import { useProfile } from "@/lib/api/queries";
import { FeedPost } from "@/components/shared/FeedPost";

const STREAM_API_KEY = process.env.EXPO_PUBLIC_STREAM_API_KEY!;
const STREAM_APP_ID = process.env.EXPO_PUBLIC_STREAM_APP_ID!;

interface EnrichedActivityWithText extends EnrichedActivity {
  text?: string;
  message?: string;
  image?: string;
  isPinned?: boolean;
  attachments?: Array<{
    type: string;
    image_url?: string;
    asset_url?: string;
    custom?: Record<string, any>;
  }>;
  own_reactions?: {
    like?: any[];
    comment?: EnrichedReaction[];
  };
  reaction_counts?: {
    like?: number;
    comment?: number;
  };
}

type CommentWithUser = EnrichedReaction & {
  user?: {
    id: string;
    created_at: string;
    updated_at: string;
    data: {
      name?: string;
      image?: string;
      avatarUrl?: string;
    };
  };
};

export default function PostDetail() {
  const { id: activityId, moduleConfig } = useLocalSearchParams<{
    id: string;
    moduleConfig: string;
  }>();
  const { streamToken, userId } = useAppContext();
  const { data: profileData } = useProfile();
  const queryClient = useQueryClient();

  const [activity, setActivity] = useState<EnrichedActivityWithText | null>(
    null
  );
  const [comments, setComments] = useState<CommentWithUser[]>([]);
  const [commentText, setCommentText] = useState("");
  const [loading, setLoading] = useState(true);
  const [loadingComments, setLoadingComments] = useState(false);
  const [posting, setPosting] = useState(false);
  const [client, setClient] = useState<any>(null);
  const [moduleConfigParsed] = useState(() =>
    moduleConfig ? JSON.parse(moduleConfig) : null
  );

  const fetchActivity = useCallback(async () => {
    if (!streamToken || !activityId || !moduleConfigParsed) return;

    try {
      setLoading(true);

      // Initialize GetStream client
      const streamClient = connect(STREAM_API_KEY, streamToken, STREAM_APP_ID);
      setClient(streamClient);

      // Get the feed
      const feed = streamClient.feed(
        moduleConfigParsed.feedGroup,
        moduleConfigParsed.feedId
      );

      // Fetch activities
      const response = await feed.get({
        id_gte: activityId,
        limit: 1,
        enrich: true,
        withReactionCounts: true,
        withOwnReactions: true,
      });

      if (response.results.length > 0) {
        setActivity(response.results[0] as EnrichedActivityWithText);
        fetchComments(streamClient, response.results[0].id);
      }
    } catch (err) {
      console.error("Error fetching activity:", err);
    } finally {
      setLoading(false);
    }
  }, [streamToken, activityId, moduleConfigParsed]);

  const fetchComments = async (streamClient: any, activityId: string) => {
    setLoadingComments(true);
    try {
      const response = await streamClient.reactions.filter({
        activity_id: activityId,
        kind: "comment",
        limit: 50,
      });
      setComments(response.results as CommentWithUser[]);
    } catch (error) {
      console.error("Error fetching comments:", error);
    } finally {
      setLoadingComments(false);
    }
  };

  useEffect(() => {
    fetchActivity();
  }, [fetchActivity]);

  const handleLike = async () => {
    if (!client || !activity || !moduleConfigParsed) return;

    try {
      const isLiked =
        activity.own_reactions?.like && activity.own_reactions.like.length > 0;

      if (isLiked) {
        // Unlike
        await client.reactions.delete(activity.own_reactions!.like![0].id);

        const updatedActivity = {
          ...activity,
          own_reactions: { ...activity.own_reactions, like: [] },
          reaction_counts: {
            ...activity.reaction_counts,
            like: Math.max((activity.reaction_counts?.like || 0) - 1, 0),
          },
        };

        setActivity(updatedActivity);

        // Update React Query cache for the feed
        updateFeedCache(updatedActivity, isLiked);
      } else {
        // Like
        const reaction = await client.reactions.add("like", activity.id);

        const updatedActivity = {
          ...activity,
          own_reactions: { ...activity.own_reactions, like: [reaction] },
          reaction_counts: {
            ...activity.reaction_counts,
            like: (activity.reaction_counts?.like || 0) + 1,
          },
        };

        setActivity(updatedActivity);

        // Update React Query cache for the feed
        updateFeedCache(updatedActivity, !!isLiked);
      }
    } catch (error) {
      console.error("Error toggling like:", error);
    }
  };

  // Helper function to update the React Query cache for the feed
  const updateFeedCache = (
    updatedActivity: EnrichedActivityWithText,
    _wasLiked: boolean
  ) => {
    const feedQueryKey = [
      "feed",
      moduleConfigParsed.feedGroup,
      moduleConfigParsed.feedId,
    ];

    queryClient.setQueryData(feedQueryKey, (old: any) => {
      if (!old) return old;

      return {
        ...old,
        pages: old.pages.map((page: any) => ({
          ...page,
          results: page.results.map((activity: any) => {
            if (activity.id !== updatedActivity.id) return activity;

            return {
              ...activity,
              reaction_counts: updatedActivity.reaction_counts,
              own_reactions: updatedActivity.own_reactions,
            };
          }),
        })),
      };
    });
  };

  const handleAddComment = async () => {
    if (!client || !activity || !commentText.trim()) return;

    setPosting(true);
    try {
      const comment = await client.reactions.add("comment", activity.id, {
        text: commentText,
      });

      // Add comment to local state with user info
      const newComment: CommentWithUser = {
        ...comment,
        user: {
          id: userId!,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          data: {
            name:
              `${profileData?.data?.firstName || ""} ${
                profileData?.data?.lastName || ""
              }`.trim() || "Anonymous",
            image: profileData?.data?.avatarUrl,
            avatarUrl: profileData?.data?.avatarUrl,
          },
        },
      };

      setComments([...comments, newComment]);
      setCommentText("");

      // Update activity comment count
      const updatedActivity = {
        ...activity,
        reaction_counts: {
          ...activity.reaction_counts,
          comment: (activity.reaction_counts?.comment || 0) + 1,
        },
      };

      setActivity(updatedActivity);

      // Update React Query cache for the feed
      updateFeedCache(updatedActivity, false);
    } catch (error) {
      console.error("Error adding comment:", error);
    } finally {
      setPosting(false);
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return "just now";
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800)
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <View style={styles.centerContainer}>
          <ActivityIndicator size="large" color="#EF5252" />
        </View>
      </SafeAreaView>
    );
  }

  if (!activity) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>Post not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />

      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <ArrowLeft size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Post</Text>
        <View style={{ width: 24 }} />
      </View>

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={0}
      >
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          {/* Post Content */}
          <FeedPost
            activity={activity}
            onLike={handleLike}
            showActions={true}
            showShare={true}
            variant="detail"
          />

          {/* Comments Section */}
          <View style={styles.commentsSection}>
            <Text style={styles.commentsTitle}>Comments</Text>

            {loadingComments ? (
              <ActivityIndicator
                size="small"
                color="#EF5252"
                style={{ marginTop: 20 }}
              />
            ) : comments.length === 0 ? (
              <Text style={styles.noCommentsText}>
                No comments yet. Be the first to comment!
              </Text>
            ) : (
              comments.map((comment) => (
                <View key={comment.id} style={styles.commentCard}>
                  <View style={styles.commentHeader}>
                    {comment.user?.data?.image ||
                    comment.user?.data?.avatarUrl ? (
                      <Image
                        source={{
                          uri:
                            comment.user.data.image ||
                            comment.user.data.avatarUrl,
                        }}
                        style={styles.commentAvatar}
                      />
                    ) : (
                      <View style={styles.commentAvatarPlaceholder}>
                        <Text style={styles.commentAvatarText}>
                          {(comment.user?.data?.name || "U")[0].toUpperCase()}
                        </Text>
                      </View>
                    )}
                    <View style={styles.commentMeta}>
                      <Text style={styles.commentUserName}>
                        {comment.user?.data?.name || "Anonymous"}
                      </Text>
                      <Text style={styles.commentTimestamp}>
                        {formatTime(comment.created_at)}
                      </Text>
                    </View>
                  </View>
                  <Text style={styles.commentText}>
                    {(comment.data as any)?.text || ""}
                  </Text>
                </View>
              ))
            )}
          </View>
        </ScrollView>

        {/* Comment Input */}
        <View style={styles.commentInputContainer}>
          <TextInput
            style={styles.commentInput}
            placeholder="Write a comment..."
            placeholderTextColor="#9A9A9A"
            value={commentText}
            onChangeText={setCommentText}
            multiline
            maxLength={500}
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              !commentText.trim() && styles.sendButtonDisabled,
            ]}
            onPress={handleAddComment}
            disabled={!commentText.trim() || posting}
          >
            {posting ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Send size={20} color="#fff" />
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#242424",
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#fff",
  },
  errorText: {
    color: "#9A9A9A",
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },

  commentsSection: {
    paddingHorizontal: 16,
  },
  commentsTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#fff",
    marginVertical: 8,
  },
  noCommentsText: {
    color: "#9A9A9A",
    fontSize: 14,
    textAlign: "center",
    marginTop: 20,
  },
  commentCard: {
    backgroundColor: "#171D23",
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  commentHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
    marginBottom: 8,
  },
  commentAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  commentAvatarPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#333",
    justifyContent: "center",
    alignItems: "center",
  },
  commentAvatarText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  commentMeta: {
    flex: 1,
  },
  commentUserName: {
    fontSize: 14,
    fontWeight: "600",
    color: "#fff",
  },
  commentTimestamp: {
    fontSize: 12,
    color: "#9A9A9A",
  },
  commentText: {
    fontSize: 14,
    color: "#D9D9D9",
    lineHeight: 20,
  },
  commentInputContainer: {
    flexDirection: "row",
    alignItems: "flex-end",
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "#242424",
    backgroundColor: "#000",
  },
  commentInput: {
    flex: 1,
    backgroundColor: "#171D23",
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 12,
    color: "#fff",
    fontSize: 14,
    maxHeight: 100,
  },
  sendButton: {
    backgroundColor: "#EF5252",
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
});
